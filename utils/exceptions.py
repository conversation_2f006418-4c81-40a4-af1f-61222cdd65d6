"""自定义异常类定义"""

from typing import Optional, Dict, Any


class BaseCustomException(Exception):
    """基础自定义异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(BaseCustomException):
    """认证相关异常"""
    
    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(message, error_code="AUTH_ERROR", **kwargs)


class AuthorizationError(BaseCustomException):
    """授权相关异常"""
    
    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(message, error_code="AUTHZ_ERROR", **kwargs)


class LDAPConnectionError(BaseCustomException):
    """LDAP连接异常"""
    
    def __init__(self, message: str = "LDAP服务连接失败", **kwargs):
        super().__init__(message, error_code="LDAP_CONN_ERROR", **kwargs)


class LDAPAuthenticationError(BaseCustomException):
    """LDAP认证异常"""
    
    def __init__(self, message: str = "LDAP认证失败", **kwargs):
        super().__init__(message, error_code="LDAP_AUTH_ERROR", **kwargs)


class DatabaseError(BaseCustomException):
    """数据库相关异常"""
    
    def __init__(self, message: str = "数据库操作失败", **kwargs):
        super().__init__(message, error_code="DB_ERROR", **kwargs)


class ValidationError(BaseCustomException):
    """数据验证异常"""
    
    def __init__(self, message: str = "数据验证失败", **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)


class RAGServiceError(BaseCustomException):
    """RAG服务异常"""
    
    def __init__(self, message: str = "RAG服务异常", **kwargs):
        super().__init__(message, error_code="RAG_ERROR", **kwargs)


class MilvusConnectionError(BaseCustomException):
    """Milvus连接异常"""
    
    def __init__(self, message: str = "Milvus连接失败", **kwargs):
        super().__init__(message, error_code="MILVUS_CONN_ERROR", **kwargs)


class LLMServiceError(BaseCustomException):
    """LLM服务异常"""
    
    def __init__(self, message: str = "LLM服务异常", **kwargs):
        super().__init__(message, error_code="LLM_ERROR", **kwargs)


class ConversationNotFoundError(BaseCustomException):
    """会话不存在异常"""
    
    def __init__(self, conversation_id: int, **kwargs):
        message = f"会话 {conversation_id} 不存在或无权限访问"
        super().__init__(message, error_code="CONVERSATION_NOT_FOUND", **kwargs)


class MessageNotFoundError(BaseCustomException):
    """消息不存在异常"""
    
    def __init__(self, message_id: int, **kwargs):
        message = f"消息 {message_id} 不存在"
        super().__init__(message, error_code="MESSAGE_NOT_FOUND", **kwargs)


class RateLimitExceededError(BaseCustomException):
    """速率限制异常"""
    
    def __init__(self, message: str = "请求频率过高，请稍后再试", **kwargs):
        super().__init__(message, error_code="RATE_LIMIT_EXCEEDED", **kwargs)


class ConfigurationError(BaseCustomException):
    """配置错误异常"""
    
    def __init__(self, message: str = "配置错误", **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)


class ExternalServiceError(BaseCustomException):
    """外部服务异常"""
    
    def __init__(self, service_name: str, message: str = None, **kwargs):
        if message is None:
            message = f"外部服务 {service_name} 异常"
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR", **kwargs)
        self.service_name = service_name
