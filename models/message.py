"""Message model for chat messages."""

from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from crud.database import Base


class Message(Base):
    """Message model for storing chat messages."""

    __tablename__ = "message"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversation.id"), nullable=False)
    role = Column(String, nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)
    references = Column(JSON, nullable=True)  # 存储引用内容的JSON数组
    parent_msg_id = Column(Integer, default=0)  # 父消息ID，第一条消息为0
    timestamp = Column(DateTime, default=datetime.utcnow)  # 保持兼容性
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")

    def __repr__(self) -> str:
        return f"<Message(id={self.id}, role='{self.role}')>"
