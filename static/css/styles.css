/* 全局样式 - 现代设计 */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.font-inter {
    font-family: 'Inter', sans-serif;
}

/* 背景动态渐变 */
body {
    background-attachment: fixed;
    background-size: 400% 400%;
    animation: subtleGradientShift 20s ease infinite;
}

@keyframes subtleGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 自定义滚动条 - 优雅简约 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.6) rgba(241, 245, 249, 0.5);
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(99, 102, 241, 0.6));
    border-radius: 10px;
    transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(99, 102, 241, 0.8));
}

/* 确保滚动条始终显示 */
.custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
}

/* 通用滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.4), rgba(100, 116, 139, 0.4));
    border-radius: 10px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.6), rgba(100, 116, 139, 0.6));
}

/* 微妙的页面加载动画 */
@keyframes pageLoad {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#app {
    animation: pageLoad 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 消息动画 - 保持原有逻辑 */
.slide-fade-enter-active {
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.7, 0, 0.84, 0);
}

.slide-fade-enter-from, .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

/* 消息容器样式 - 保持原有功能 */
.chat-container {
    height: calc(100vh - 100px);
}

.messages-container {
    height: calc(100vh - 220px);
}

/* 对话列表容器样式 */
.conversation-list-container {
    min-height: 200px;
    max-height: calc(100vh - 200px);
    position: relative;
}

/* 分组标题样式 - 跟随滚动但不粘在顶部 */
.conversation-list-container .sticky {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    margin-bottom: 8px;
    padding: 12px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgb(100, 116, 139);
    transition: all 0.3s ease;
}

/* 分组标题悬停效果 */
.conversation-list-container .sticky:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

/* 消息气泡样式 - 现代设计但保持原有结构 */
.message-container {
    max-width: 75%;
    word-wrap: break-word;
    overflow-wrap: break-word;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.message-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.message-container:hover::before {
    left: 100%;
}

.message-container.user-message {
    border-radius: 24px 24px 6px 24px;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    box-shadow: 
        0 8px 32px rgba(59, 130, 246, 0.15),
        0 2px 8px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    transform-origin: bottom right;
}

.message-container.user-message:hover {
    transform: scale(1.02);
    box-shadow: 
        0 12px 40px rgba(59, 130, 246, 0.2),
        0 4px 16px rgba(59, 130, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.message-container.expanded {
    max-width: 85%;
}

/* 引用内容样式 - 保持原有功能 */
.references-section {
    border-top: 1px solid #e5e7eb;
    margin-top: 12px;
    padding-top: 12px;
}

.reference-item {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.reference-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 单个引用项样式 */
.reference-header {
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 8px 8px 0 0;
}

.reference-header:hover {
    background-color: #f3f4f6;
}

.reference-content {
    border-top: 1px solid #e5e7eb;
    padding: 12px;
    background-color: #ffffff;
    border-radius: 0 0 8px 8px;
}

.reference-title {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.25;
}

.reference-preview {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 4px;
    line-height: 1.4;
}

.reference-score {
    color: #6b7280;
    font-size: 0.75rem;
}

.reference-expand-icon {
    transition: transform 0.2s ease;
    color: #9ca3af;
}

.reference-expand-icon.expanded {
    transform: rotate(180deg);
}

/* 引用内容中的特殊样式 */
.reference-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.reference-title {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.reference-score {
    font-size: 0.75rem;
    color: #6b7280;
}

.reference-content {
    color: #4b5563;
    font-size: 0.75rem;
    line-height: 1.5;
    margin-bottom: 8px;
}

.reference-source {
    font-size: 0.75rem;
}

.reference-source a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

.reference-source a:hover {
    color: #1d4ed8;
}

/* Markdown内容样式 */
.prose {
    max-width: none;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.prose.prose-xs {
    font-size: 0.75rem;
    line-height: 1.5;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    word-wrap: break-word;
}

.prose p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    word-wrap: break-word;
}

.prose ul, .prose ol {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose li {
    word-wrap: break-word;
}

.prose code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.prose pre {
    background-color: #f3f4f6;
    padding: 0.75rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5em 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.prose blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 0.5em 0;
    color: #6b7280;
    word-wrap: break-word;
}

/* 引用内容中的特殊样式 */
.prose.prose-xs h1, .prose.prose-xs h2, .prose.prose-xs h3 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

.prose.prose-xs p {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

.prose.prose-xs ul, .prose.prose-xs ol {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
    padding-left: 1rem;
}

.prose.prose-xs code {
    font-size: 0.6875rem;
}

.prose.prose-xs pre {
    font-size: 0.6875rem;
    padding: 0.5rem;
}

/* 对话菜单样式 */
.conversation-menu {
    animation: menuSlideIn 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    backdrop-filter: blur(20px);
    z-index: 50;
}

/* 菜单按钮样式 - 防止闪烁 */
.conversation-menu-button {
    transition: opacity 0.3s ease;
}

.conversation-menu-button.active {
    opacity: 1 !important;
}

/* 当菜单打开时，禁用其他对话项的hover效果 */
.conversation-list-container.menu-open .group:not(.menu-active) {
    pointer-events: none;
}

.conversation-list-container.menu-open .group:not(.menu-active) * {
    pointer-events: none;
}

/* 确保菜单打开时，只有当前对话项可交互 */
.conversation-list-container.menu-open .group.menu-active {
    pointer-events: auto;
}

.conversation-list-container.menu-open .group.menu-active * {
    pointer-events: auto;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .message-container {
        max-width: 85%;
    }
    
    .message-container.user-message {
        max-width: 80%;
    }
}

@media (max-width: 768px) {
    .message-container {
        max-width: 90%;
    }

    .message-container.expanded {
        max-width: 95%;
    }

    .message-container.user-message {
        max-width: 85%;
    }

    .reference-item {
        padding: 8px;
    }

    .prose.prose-xs {
        font-size: 0.6875rem;
    }
}

@media (max-width: 640px) {
    .message-container {
        max-width: 95%;
    }

    .message-container.expanded {
        max-width: 98%;
    }

    .message-container.user-message {
        max-width: 90%;
    }

    .reference-item {
        padding: 6px;
    }
}

/* 高级动画效果 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

/* 特殊状态样式 */
.reference-card.expanded {
    animation: float 3s ease-in-out infinite;
}

/* 加载动画优化 */
@keyframes pulse-soft {
    0%, 100% { 
        opacity: 0.4;
        transform: scale(1);
    }
    50% { 
        opacity: 1;
        transform: scale(1.1);
    }
}

.animate-pulse {
    animation: pulse-soft 1.5s ease-in-out infinite;
}

/* 过渡效果增强 */
.transition-all {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 玻璃态效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 8px 32px rgba(31, 38, 135, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 渐变背景增强 */
.gradient-bg {
    background: linear-gradient(135deg, 
        rgba(99, 102, 241, 0.1) 0%,
        rgba(59, 130, 246, 0.05) 25%,
        rgba(16, 185, 129, 0.05) 50%,
        rgba(245, 101, 101, 0.05) 75%,
        rgba(168, 85, 247, 0.1) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 微交互增强 */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 40px rgba(100, 116, 139, 0.12),
        0 4px 16px rgba(100, 116, 139, 0.08);
}

/* 焦点状态优化 */
.focus-visible:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 8px;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .prose {
        color: #e2e8f0;
    }
    
    .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
        color: #f1f5f9;
    }
    
    .prose code {
        background: rgba(99, 102, 241, 0.2);
        color: #a5b4fc;
    }
    
    .prose blockquote {
        color: #94a3b8;
        background: rgba(99, 102, 241, 0.1);
    }
}