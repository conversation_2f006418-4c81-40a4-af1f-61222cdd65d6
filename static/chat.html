<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银通知识助手</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-inter">
    <div id="app" class="h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40">
        <!-- 聊天主页面 -->
        <div class="flex flex-col h-full">
            <!-- 顶部导航栏 - 现代设计 -->
            <header class="backdrop-blur-xl bg-white/80 border-b border-white/20 shadow-sm shadow-blue-100/50">
                <div class="flex items-center justify-between px-8 py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg shadow-blue-200/50">
                                <img src="images/logo.png" alt="Logo" class="h-6 w-6" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="h-6 w-6 flex items-center justify-center text-white font-bold text-sm" style="display: none;">银</div>
                            </div>
                        
                            <!-- 重命名对话弹窗 -->
                            <div v-if="showRenameModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
                                <div class="bg-white rounded-2xl shadow-xl p-6 w-96 max-w-90vw">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">重命名对话</h3>
                                    <input 
                                        v-model="newConversationTitle" 
                                        @keyup.enter="confirmRename"
                                        ref="renameInput"
                                        type="text" 
                                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="请输入新的对话标题">
                                    <div class="flex justify-end space-x-3 mt-6">
                                        <button @click="cancelRename" 
                                                class="px-4 py-2 text-gray-600 hover:text-gray-800 rounded-lg transition-colors">
                                            取消
                                        </button>
                                        <button @click="confirmRename" 
                                                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                            确认
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h1 class="text-xl font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">银通知识助手</h1>
                            <p class="text-xs text-slate-500 mt-0.5">智能知识助手</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-sm text-slate-600 bg-white/60 backdrop-blur-sm rounded-2xl px-4 py-2 border border-white/40 shadow-sm">
                            <i class="fas fa-user-circle mr-2 text-slate-400"></i>{{ currentUser }}
                        </div>
                        <button @click="handleLogout"
                            class="flex items-center text-sm text-slate-600 hover:text-slate-800 bg-white/60 hover:bg-white/80 backdrop-blur-sm rounded-2xl px-4 py-2 border border-white/40 shadow-sm hover:shadow-md transition-all duration-300">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出
                        </button>
                    </div>
                </div>
            </header>

            <!-- 主体内容 -->
            <div class="flex flex-1 overflow-hidden p-6 gap-6">
                <!-- 左侧面板 - 现代卡片设计 -->
                <div :class="['flex flex-col transition-all duration-500 ease-out', 
                             isSidebarCollapsed ? 'w-20' : 'w-80']">
                    <div class="bg-white/70 backdrop-blur-xl border border-white/30 rounded-3xl shadow-xl shadow-blue-100/20 overflow-hidden h-full">
                        <!-- 面板头部 -->
                        <div class="flex items-center justify-between p-6 border-b border-slate-100/80">
                            <div v-if="!isSidebarCollapsed" class="flex-1">
                                <button @click="createNewConversation"
                                    class="group w-full flex items-center justify-center py-3 px-6 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-2xl shadow-lg shadow-blue-200/50 hover:shadow-xl hover:shadow-blue-300/60 transition-all duration-300 font-medium">
                                    <i class="fas fa-plus mr-3 group-hover:rotate-90 transition-transform duration-300"></i>
                                    新建对话
                                </button>
                            </div>
                            <button @click="toggleSidebar"
                                class="p-3 rounded-2xl hover:bg-slate-50 text-slate-400 hover:text-slate-600 transition-all duration-300 group">
                                <i :class="[isSidebarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left', 
                                           'group-hover:scale-110 transition-transform duration-300']"></i>
                            </button>
                        </div>

                        <!-- 对话列表 -->
                        <div :class="[
                            'flex-1 overflow-y-auto custom-scrollbar conversation-list-container',
                            openConversationMenuId ? 'menu-open' : ''
                        ]">
                            <!-- 时间分组显示 -->
                            <div v-for="group in groupedConversations" :key="group.title" class="px-4">
                                <!-- 分组标题 -->
                                <div v-if="!isSidebarCollapsed && group.conversations.length > 0"
                                     class="sticky bg-white/80 backdrop-blur-sm px-2 py-3 text-xs font-semibold text-slate-500 uppercase tracking-wider border-b border-slate-100/60 mb-2 z-10">
                                    {{ group.title }}
                                </div>

                                <!-- 分组内的对话 -->
                                <div class="space-y-2 mb-6">
                                    <div v-for="conversation in group.conversations" :key="conversation.id"
                                        @click="selectConversation(conversation.id)"
                                        :class="[
                                            'group relative p-4 rounded-2xl cursor-pointer transition-all duration-300',
                                            openConversationMenuId ? '' : 'hover:scale-[1.02]',
                                            openConversationMenuId === conversation.id ? 'menu-active' : '',
                                            currentConversationId === conversation.id 
                                                ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 shadow-md shadow-blue-100/50' 
                                                : openConversationMenuId ? '' : 'hover:bg-white/80 hover:shadow-lg hover:shadow-slate-200/50',
                                            conversation.sticky_flag ? 'ring-2 ring-amber-200 bg-amber-50/50' : ''
                                        ]">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center mb-2">
                                                    <!-- 置顶图标 -->
                                                    <i v-if="!isSidebarCollapsed && conversation.sticky_flag"
                                                       class="fas fa-star text-amber-500 text-xs mr-2"></i>
                                                    <p v-if="!isSidebarCollapsed" class="text-sm font-medium text-slate-800 truncate leading-relaxed">
                                                        {{ conversation.title }}
                                                    </p>
                                                </div>
                                                <p v-if="!isSidebarCollapsed" class="text-xs text-slate-500 mb-1">
                                                    {{ formatTime(conversation.updated_at) }}
                                                </p>

                                            </div>
                                            <div v-if="!isSidebarCollapsed && conversations.length > 1" class="relative" 
                                                 @mouseleave="setTimeout(() => { if (openConversationMenuId === conversation.id) openConversationMenuId = null; }, 300)">
                                                <button @click.stop="toggleConversationMenu(conversation.id)"
                                                    :class="[
                                                        'p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-xl transition-all duration-300 conversation-menu-button',
                                                        openConversationMenuId === conversation.id ? 'opacity-100 active' : openConversationMenuId ? 'opacity-0' : 'opacity-0 group-hover:opacity-100'
                                                    ]">
                                                    <i class="fas fa-ellipsis-vertical"></i>
                                                </button>
                                                <!-- 菜单弹窗 -->
                                                <div v-if="openConversationMenuId === conversation.id"
                                                     class="absolute right-0 top-12 w-48 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-white/40 py-2 z-20 conversation-menu"
                                                     @mouseleave="openConversationMenuId = null">
                                                    <button @click.stop="renameConversation(conversation.id)"
                                                        class="flex items-center w-full px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200">
                                                        <i class="fas fa-edit mr-3"></i>
                                                        重命名对话
                                                    </button>
                                                    <button @click.stop="pinConversation(conversation.id)"
                                                        class="flex items-center w-full px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200">
                                                        <i :class="[
                                                            'fas mr-3',
                                                            conversation.sticky_flag ? 'fa-star text-amber-500' : 'fa-star text-slate-400'
                                                        ]"></i>
                                                        {{ conversation.sticky_flag ? '取消置顶' : '置顶对话' }}
                                                    </button>
                                                    <button @click.stop="deleteConversation(conversation.id)"
                                                        class="flex items-center w-full px-4 py-3 text-sm text-red-500 hover:bg-red-50 transition-colors duration-200">
                                                        <i class="fas fa-trash mr-3"></i>
                                                        删除对话
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧聊天区域 - 沉浸式设计 -->
                <div class="flex flex-1 bg-gradient-to-br from-gray-50 to-gray-100">
                    <!-- 主聊天区域 -->
                    <div class="flex flex-col flex-1">
                        <!-- 知识库和过滤条件选择面板 -->
                        <div v-if="currentConversationId && isTemporaryConversation" class="bg-white/70 backdrop-blur-xl border border-white/30 rounded-3xl shadow-lg shadow-blue-100/20 p-6 m-4 mb-0">
                            <div class="flex flex-wrap gap-6">
                                <!-- 知识库选择 -->
                                <div class="flex-1 min-w-64">
                                    <label class="block text-sm font-semibold text-slate-700 mb-3">
                                        <i class="fas fa-database mr-2 text-blue-500"></i>知识库选择
                                    </label>
                                    <select v-model="selectedCollection" @change="handleCollectionChange"
                                            class="w-full px-4 py-3 bg-white/80 border border-slate-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm hover:shadow-md">
                                        <option value="">请选择知识库</option>
                                        <option v-for="collection in collectionsConfig" :key="collection.name" :value="collection.name">
                                            {{ collection.display_name }}
                                        </option>
                                    </select>
                                </div>

                                <!-- 动态过滤条件 -->
                                <div v-for="filterItem in currentFilterConfig" :key="filterItem.name" class="flex-1 min-w-64">
                                    <label class="block text-sm font-semibold text-slate-700 mb-3">
                                        <i class="fas fa-filter mr-2 text-indigo-500"></i>{{ filterItem.display_name }}
                                    </label>
                                    <select v-model="selectedFilters[filterItem.name]"
                                            class="w-full px-4 py-3 bg-white/80 border border-slate-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 shadow-sm hover:shadow-md">
                                        <option value="">请选择{{ filterItem.display_name }}</option>
                                        <option v-for="option in filterItem.options" :key="option" :value="option">
                                            {{ option }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- 消息显示区域 -->
                        <div ref="messagesContainer" class="messages-container overflow-y-auto p-6 m-4 bg-white/60 backdrop-blur-xl border border-white/30 rounded-3xl shadow-xl shadow-blue-100/20"
                             :class="currentConversationId && isTemporaryConversation ? 'rounded-b-3xl' : 'rounded-3xl'">
                        <div v-if="!currentConversationId" class="flex items-center justify-center h-full">
                            <div class="text-center max-w-md">
                                <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                                    <i class="fas fa-comments text-3xl text-blue-500"></i>
                                </div>
                                <h3 class="text-2xl font-semibold text-slate-700 mb-4">开始新的对话</h3>
                                <p class="text-slate-500 mb-8 leading-relaxed">选择或创建一个对话来开始与AI助手的智能问答</p>
                                <button @click="createNewConversation"
                                    class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-2xl shadow-lg shadow-blue-200/50 hover:shadow-xl hover:shadow-blue-300/60 transition-all duration-300 font-medium group">
                                    <i class="fas fa-plus mr-3 group-hover:rotate-90 transition-transform duration-300"></i>
                                    新建对话
                                </button>
                            </div>
                        </div>
                        <div v-else>
                            <transition-group name="slide-fade" tag="div">
                                <div v-for="(message, index) in messages" :key="index" class="mb-4">
                                    <div :class="[
                                        'px-4 py-3 rounded-2xl shadow-sm message-container',
                                        message.role === 'user'
                                            ? 'ml-auto bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none user-message'
                                            : 'mr-auto bg-gray-100 text-gray-800 rounded-bl-none',
                                        message.role === 'assistant' && message.references && message.references.length > 0 && message.referencesExpanded
                                            ? 'expanded' : ''
                                    ]">
                                        <!-- 消息内容 -->
                                        <div v-if="message.role === 'user'" class="whitespace-pre-wrap">{{ message.content }}</div>
                                        <div v-else v-html="renderMarkdown(message.content)" class="prose prose-sm max-w-none"></div>

                                        <!-- 引用内容（仅AI消息显示） -->
                                        <div v-if="message.role === 'assistant' && message.references && message.references.length > 0" class="mt-3 border-t border-gray-200 pt-3">
                                            <div class="text-sm font-medium text-gray-600 mb-3">
                                                <i class="fas fa-book-open mr-1"></i>
                                                参考资料 ({{ message.references.length }})
                                            </div>

                                            <!-- 引用内容列表 - 每个引用单独折叠 -->
                                            <div class="space-y-2 max-w-full">
                                                <div v-for="(ref, refIndex) in message.references" :key="refIndex"
                                                     class="bg-gray-50 border border-gray-300 rounded-lg text-sm">

                                                    <!-- 引用标题栏 - 可点击展开/折叠 -->
                                                    <div class="flex items-start justify-between p-3 cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                                                         @click="toggleSingleReference(index, refIndex)">
                                                        <div class="flex-1 min-w-0 pr-2">
                                                            <div class="font-medium text-gray-700 truncate">{{ ref.title }}</div>
                                                            <div class="text-xs text-gray-500 mt-1">
                                                                相关度: {{ (ref.score * 100).toFixed(1) }}%
                                                                <span v-if="!ref.expanded" class="ml-2">
                                                                    {{ ref.content.substring(0, 60) }}...
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <i :class="[
                                                            'fas text-gray-400 transition-transform duration-200 flex-shrink-0 mt-1',
                                                            ref.expanded ? 'fa-chevron-up' : 'fa-chevron-down'
                                                        ]"></i>
                                                    </div>

                                                    <!-- 引用详细内容 - 展开时显示 -->
                                                    <div v-if="ref.expanded" class="px-3 pb-3 border-t border-gray-200">
                                                        <div class="text-gray-600 text-xs leading-relaxed prose prose-xs max-w-none mt-2"
                                                             v-html="renderMarkdown(ref.content)"></div>
                                                        <div v-if="ref.source" class="mt-3 pt-2 border-t border-gray-200">
                                                            <a :href="ref.source" target="_blank" class="text-blue-500 hover:text-blue-700 text-xs">
                                                                <i class="fas fa-external-link-alt mr-1"></i>查看来源
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-xs mt-1 opacity-70">
                                            {{ formatTime(message.timestamp) }}
                                        </div>
                                    </div>
                                </div>
                            </transition-group>
                            
                            <!-- 加载状态指示器 -->
                            <div v-if="isReceiving" class="mb-4">
                                <div class="mr-auto bg-gray-100 text-gray-800 rounded-2xl rounded-bl-none px-4 py-3 shadow-sm" style="width: fit-content;">
                                    <div class="flex items-center">
                                        <i class="fas fa-circle text-xs mr-1 animate-pulse text-blue-400"></i>
                                        <i class="fas fa-circle text-xs mr-1 animate-pulse text-blue-400" style="animation-delay: 0.2s"></i>
                                        <i class="fas fa-circle text-xs animate-pulse text-blue-400" style="animation-delay: 0.4s"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- 输入区域 -->
                        <div v-if="currentConversationId" class="px-4 pb-6">
                            <div class="flex bg-white/90 backdrop-blur-sm border border-white/40 rounded-3xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-2">
                                <div class="flex-1 mr-2">
                                    <textarea v-model="newMessage" @keydown="handleKeyDown" ref="messageInput"
                                        placeholder="输入消息..." rows="2"
                                        class="block w-full px-4 py-3 border-0 rounded-3xl placeholder-gray-400 focus:outline-none focus:ring-0 resize-none bg-transparent text-gray-700"></textarea>
                                </div>
                                <div class="flex items-end">
                                    <button @click="sendMessage" :disabled="!newMessage.trim() || isSending || (isTemporaryConversation && !selectedCollection)"
                                        class="flex items-center justify-center h-12 w-12 rounded-2xl shadow-sm text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 focus:outline-none disabled:opacity-50 transition duration-200 group">
                                        <i :class="[isSending ? 'fas fa-spinner fa-spin' : 'fas fa-paper-plane', 
                                                   'group-hover:scale-110 transition-transform duration-200']"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500 text-center">
                                <span v-if="isTemporaryConversation && !selectedCollection" class="text-orange-500">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>请先选择知识库
                                </span>
                                <span v-else>Enter发送消息，Shift+Enter换行</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/chat.js"></script>
</body>
</html>
