<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银通知识助手 - 登录</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-inter">
    <div id="app" class="h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40">
        <div class="flex items-center justify-center h-full px-6">
            <div class="w-full max-w-md">
                <!-- 登录卡片 -->
                <div class="bg-white/70 backdrop-blur-xl border border-white/30 rounded-3xl shadow-xl shadow-blue-100/20 p-8 space-y-8">
                    <!-- 头部区域 -->
                    <div class="text-center">
                        <div class="mx-auto w-20 h-20 rounded-3xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg shadow-blue-200/50 mb-6">
                            <img src="images/logo.png" alt="Logo" class="h-12 w-12" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="h-12 w-12 flex items-center justify-center text-white font-bold text-2xl" style="display: none;">银</div>
                        </div>
                        <h2 class="text-3xl font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">银通知识助手</h2>
                        <p class="text-slate-500 font-medium">智能知识助手</p>
                        <p class="text-sm text-slate-400 mt-2">请登录您的账户</p>
                    </div>

                    <!-- 登录表单 -->
                    <form class="space-y-6" @submit.prevent="handleLogin">
                        <div class="space-y-5">
                            <div class="group">
                                <label for="username" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>用户名
                                </label>
                                <input id="username" 
                                       name="username" 
                                       type="text" 
                                       required 
                                       v-model="loginForm.username"
                                       class="w-full px-4 py-3 bg-white/80 border border-slate-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 shadow-sm hover:shadow-md placeholder-slate-400"
                                       placeholder="请输入用户名">
                            </div>
                            <div class="group">
                                <label for="password" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i class="fas fa-lock mr-2 text-indigo-500"></i>密码
                                </label>
                                <input id="password" 
                                       name="password" 
                                       type="password" 
                                       required 
                                       v-model="loginForm.password"
                                       class="w-full px-4 py-3 bg-white/80 border border-slate-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-300 shadow-sm hover:shadow-md placeholder-slate-400"
                                       placeholder="请输入密码">
                            </div>
                        </div>

                        <!-- 错误提示 -->
                        <div v-if="loginError" class="bg-red-50/80 backdrop-blur-sm border border-red-200/60 rounded-2xl p-4">
                            <div class="flex items-center text-red-600">
                                <i class="fas fa-exclamation-circle mr-2"></i>
                                <span class="text-sm font-medium">{{ loginError }}</span>
                            </div>
                        </div>

                        <!-- 登录按钮 -->
                        <button type="submit" 
                                :disabled="isLoggingIn"
                                class="group w-full flex items-center justify-center py-4 px-6 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-2xl shadow-lg shadow-blue-200/50 hover:shadow-xl hover:shadow-blue-300/60 transition-all duration-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <span v-if="isLoggingIn" class="mr-3">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                            <span v-else class="mr-3">
                                <i class="fas fa-sign-in-alt group-hover:scale-110 transition-transform duration-200"></i>
                            </span>
                            {{ isLoggingIn ? '登录中...' : '登录' }}
                        </button>
                    </form>

                    <!-- 底部装饰 -->
                    <div class="text-center pt-4 border-t border-slate-100/60">
                        <p class="text-xs text-slate-400">
                            <i class="fas fa-shield-alt mr-1"></i>
                            安全登录 · 数据保护
                        </p>
                    </div>
                </div>

                <!-- 背景装饰元素 -->
                <div class="absolute inset-0 overflow-hidden pointer-events-none">
                    <div class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 rounded-full blur-3xl"></div>
                    <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-400/10 to-pink-500/10 rounded-full blur-3xl"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/login.js"></script>
</body>
</html>