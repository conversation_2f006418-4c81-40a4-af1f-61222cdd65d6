"""Collections API routes."""

from fastapi import APIRouter, Depends
from typing import List
import logging

from config.settings import settings
from schemas.collection import CollectionConfig, FilterOption, CollectionConfigListResponse
from schemas.response import StandardErrorResponse
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api/collections", tags=["知识库配置"])
logger = logging.getLogger(__name__)


@router.get("/config", response_model=CollectionConfigListResponse, summary="获取知识库配置")
async def get_collections_config(
    current_username: str = Depends(get_current_username)
):
    """获取所有知识库及其过滤条件配置"""
    try:
        logger.info(f"[get_collections_config] 用户: {current_username} 请求知识库配置")
        
        collections = []
        for key, value in settings.COLLECTIONS_CONFIG.items():
            # 构建过滤条件列表
            filter_options = []
            for filter_item in value.get("filter", []):
                filter_option = FilterOption(
                    name=filter_item["name"],
                    display_name=filter_item["display_name"],
                    description=filter_item["description"],
                    options=filter_item["options"]
                )
                filter_options.append(filter_option)
            
            # 构建知识库配置
            collection_config = CollectionConfig(
                name=key,
                display_name=value["display_name"],
                description=value["description"],
                filter=filter_options
            )
            collections.append(collection_config)
        
        logger.info(f"[get_collections_config] 返回 {len(collections)} 个知识库配置")
        
        return CollectionConfigListResponse(
            code=200,
            message="success",
            data=collections
        )
        
    except Exception as e:
        logger.error(f"[get_collections_config] 获取知识库配置失败: {str(e)}")
        return StandardErrorResponse(
            code=1005,
            message="获取知识库配置失败"
        )
