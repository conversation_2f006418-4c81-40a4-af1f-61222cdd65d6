# 对话重命名功能设计文档

## 1. 需求概述

为聊天应用添加对话重命名功能，允许用户通过对话菜单中的"重命名"选项修改对话标题。该功能需要前后端协同实现，包括前端UI交互和后端API接口。

## 2. 系统分析

### 2.1 当前系统架构

通过分析现有代码，我们了解到系统采用前后端分离架构：

- 前端：Vue.js + Tailwind CSS
- 后端：Python FastAPI
- 数据库：关系型数据库（通过SQLAlchemy操作）

### 2.2 相关组件

- 前端对话菜单组件（chat.html）
- ConversationService（后端服务层）
- Conversation DAO（数据访问层）
- Conversation模型（数据模型层）

## 3. 功能设计

### 3.1 前端设计

1. 在对话菜单中添加"重命名"选项
2. 点击"重命名"后，显示输入框让用户输入新标题
3. 用户确认后，调用后端API更新对话标题
4. 更新成功后，前端界面同步更新

### 3.2 后端设计

1. 添加PUT `/api/conversations/{conversation_id}/title`接口
2. 接收新标题参数
3. 调用ConversationService的update_title方法更新标题
4. 返回标准响应格式

## 4. 数据模型设计

### 4.1 Conversation模型

现有Conversation模型已经支持title字段，无需修改。

### 4.2 请求/响应数据结构

#### 4.2.1 更新标题请求
```json
{
  "conversation_id": "对话ID",
  "title": "新对话标题"
}
```


#### 4.2.2 更新标题响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conversation_id": "对话ID",
    "title": "新对话标题"
  }
}
```


## 5. API接口设计

### 5.1 接口规范

- **URL**: `/api/conversations/{conversation_id}/title`
- **方法**: PUT
- **认证**: Bearer Token
- **请求体**: 
  ```json
  {
    "conversation_id": "对话ID",
    "title": "string"
  }
  ```

- **响应格式**: 
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "conversation_id": "对话ID",
      "title": "新标题"
    }
  }
  ```


### 5.2 错误处理
- 使用项目中统一的错误处理方式

## 6. 前端实现方案

### 6.1 UI交互流程

1. 用户点击对话项的三个点按钮
2. 显示菜单选项（包括新增的"重命名"选项）
3. 用户点击"重命名"选项
4. 弹出输入框，预填当前标题
5. 用户输入新标题并确认
6. 调用后端API更新标题
7. 更新成功后，前端同步更新对话标题

### 6.2 代码修改点

1. chat.html: 在对话菜单中添加重命名选项
2. chat.js: 添加重命名相关方法

## 7. 后端实现方案

### 7.1 API路由

在[api/conversations.py](file:///D:/code/chatbot-ui/chatbot/backend/api/conversations.py)中添加新路由

### 7.2 Service层

使用现有的ConversationService.update_title方法

### 7.3 DAO层

使用现有的Conversation DAO实现

## 8. 安全与验证

1. 验证用户身份
2. 验证用户是否有权限修改该对话
3. 验证标题长度和格式
4. 防止XSS攻击（前端需对输入进行适当处理）
