## 接口标准响应格式设计文档

### 1. 背景说明

为了保证前后端数据交互的一致性、可维护性和可读性，需制定统一的接口响应格式标准。此标准适用于所有 HTTP 接口，覆盖成功、失败、分页、错误等通用场景。

---

### 2. 通用响应结构

所有接口的响应均使用统一的 JSON 格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

#### 字段说明：

* `code`：整型，业务状态码，200 表示成功，非 200 表示失败，；
* `message`：字符串，描述状态的文字信息；
* `data`：对象/数组/字符串等，接口返回的具体业务数据，失败时可为 null 或 {}。

---

### 3. 状态码约定

| code | 含义         | 示例 message          |
| ---- | ------------ | --------------------- |
| 200  | 成功         | success               |
| 1001 | 参数错误     | invalid parameters    |
| 1002 | 未授权       | unauthorized          |
| 1003 | 权限不足     | forbidden             |
| 1004 | 资源未找到   | not found             |
| 1005 | 服务内部错误 | internal server error |

---

### 4. 示例：成功响应

#### 4.1 返回单个对象：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "user_id": "123",
    "name": "Alice"
  }
}
```

#### 4.2 返回列表分页数据：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 10,
    "items": [
      { "id": 1, "title": "Doc A" },
      { "id": 2, "title": "Doc B" }
    ]
  }
}
```

---

### 5. 示例：失败响应

#### 5.1 参数错误

```json
{
  "code": 1001,
  "message": "missing required field 'user_id'",
  "data": null
}
```

#### 5.2 未授权访问

```json
{
  "code": 1002,
  "message": "authentication required",
  "data": null
}
```

---

### 6. 说明与扩展

* `code` 字段建议与 HTTP 状态码搭配使用（如 401 对应 code 1002），便于调试与日志分析；
* `message` 供开发调试与前端提示使用，正式环境应避免暴露敏感信息；
* `data` 内容结构由具体接口定义，可为空对象或 null；
* 所有响应必须符合 JSON 规范。

---

### 7. 附录

* 可定义统一的异常处理中间件返回上述格式；
* 建议提供错误码枚举文档供前端参照；
* 支持国际化 message 替换机制（如中英文切换）。
