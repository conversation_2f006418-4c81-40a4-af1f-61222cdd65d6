# 详细UI设计文档

## 1. 总体概述

### 1.1 项目背景
本项目是一个基于FastAPI的RAG聊天系统，提供用户认证、对话管理、消息交互等功能。

### 1.2 技术选型
- 前端框架：Vue
- 样式库：TailwindCSS
- UI组件库：shadcn-ui
- 聊天组件：shadcn-chat
- 动画库：Framer Motion
- 图标库：Lucide Icons

### 1.3 设计原则
- 用户友好：界面简洁直观，操作流程清晰
- 响应式设计：适配不同屏幕尺寸，支持移动端访问
- 一致性：保持整体风格和交互方式统一
- 实时性：支持SSE流式消息显示
- 非前后端分离

## 2. 页面设计

### 2.1 登录页面

#### 2.1.1 页面结构
```
+--------------------------------------------------+
|                                                  |
|                  Logo区域                        |
|                                                  |
|              +----------------+                  |
|              |   用户名输入   |                  |
|              +----------------+                  |
|                                                  |
|              +----------------+                  |
|              |   密码输入     |                  |
|              +----------------+                  |
|                                                  |
|              +----------------+                  |
|              |    登录按钮    |                  |
|              +----------------+                  |
|                                                  |
|                                                  |
+--------------------------------------------------+
```

#### 2.1.2 组件说明
1. **Logo区域**
   - 展示应用Logo和名称
   - 居中显示

2. **用户名输入框**
   - 类型：文本输入框
   - 占位符：请输入用户名
   - 校验：必填项

3. **密码输入框**
   - 类型：密码输入框
   - 占位符：请输入密码
   - 校验：必填项

4. **登录按钮**
   - 文本：登录
   - 状态：默认启用，提交时禁用并显示加载状态
   - 功能：提交登录请求

#### 2.1.3 交互流程
1. 用户输入用户名和密码
2. 点击登录按钮
3. 前端校验输入内容
4. 发送登录请求到后端`/api/login`接口
5. 根据响应结果：
   - 成功：保存JWT令牌，跳转到聊天主页面
   - 失败：显示错误提示信息

### 2.2 聊天主页面

#### 2.2.1 页面布局
```
+-------------------------------------------------------------+
| 顶部导航栏                                                  |
| [Logo] 应用名称                    用户信息 [用户头像]      |
+----------------------+--------------------------------------+
|                      |                                      |
|      左侧面板        |          右侧聊天区域                |
|                      |                                      |
| [+] 新建对话         |  +--------------------------------+  |
|                      |  |                                |  |
| [对话1]              |  |  消息1                         |  |
| [对话2]              |  |  消息2                         |  |
| [对话3]              |  |  ...                           |  |
| ...                  |  |                                |  |
|                      |  |  [加载中指示器]                |  |
|                      |  +--------------------------------+  |
|                      |                                      |
|                      |  +--------------------------------+  |
|                      |  |  [输入框]        [发送按钮]    |  |
|                      |  +--------------------------------+  |
+----------------------+--------------------------------------+
```

#### 2.2.2 左侧面板设计

##### 2.2.2.1 面板头部
- Logo及应用名称
- 折叠按钮：点击后隐藏左侧面板，只显示图标

##### 2.2.2.2 新建对话按钮
- 图标：[+] 
- 文本：新建对话
- 功能：调用`/api/conversations/new`接口创建新对话

##### 2.2.2.3 对话列表
- 显示用户所有对话记录
- 每个对话项包含：
  - 对话标题（默认为对话创建时间）
  - 最后一条消息预览
  - 时间戳
- 点击对话项切换到对应对话

#### 2.2.3 右侧聊天区域设计

##### 2.2.3.1 消息显示区域
- 垂直滚动布局
- 用户消息（右侧对齐，不同背景色）
- AI助手消息（左侧对齐，不同背景色）
- 加载状态指示器（显示AI正在输入）
- 错误消息显示

##### 2.2.3.2 输入区域
- 文本输入框：
  - 支持多行输入
  - 自适应高度
  - Enter发送消息，Shift+Enter换行
- 发送按钮：
  - 图标：发送箭头
  - 功能：发送消息到后端
  
#### 2.2.4 组件交互

##### 2.2.4.1 对话管理
1. 点击"新建对话"按钮
   - 调用`POST /api/conversations/new`接口
   - 成功后在对话列表顶部添加新对话项
   - 切换到新对话

2. 点击对话列表项
   - 加载该对话的所有消息
   - 调用`GET /api/messages?conversation_id={id}`接口
   - 清空当前消息区域并显示历史消息

3. 删除对话
   - 在对话项上右键或显示删除按钮
   - 调用`DELETE /api/conversations/{conversation_id}`接口
   - 成功后从列表中移除该对话项

##### 2.2.4.2 消息交互
1. 发送消息
   - 用户在输入框输入消息
   - 点击发送按钮或按Enter键
   - 调用`POST /api/chat/stream`接口（流式）
   - 在消息区域显示用户消息
   - 显示加载状态指示器

2. 接收流式响应
   - 实时接收SSE数据流
   - 解析`data: {"content": "消息内容"}`格式数据
   - 实时更新AI助手消息
   - 接收到`data: [DONE]`时结束流式接收
   - 隐藏加载状态指示器

3. 错误处理
   - 解析`data: {"error": "错误信息"}`格式数据
   - 显示错误消息
   - 结束流式接收

## 3. 组件设计

### 3.1 登录组件
- 表单组件：包含用户名、密码输入框和登录按钮
- 验证逻辑：前端基础验证
- 状态管理：加载状态、错误状态

### 3.2 对话列表组件
- 列表容器：展示对话项列表
- 对话项组件：单个对话的展示和操作
- 新建按钮：创建新对话的触发器

### 3.3 聊天消息组件
- 消息容器：包含所有消息项
- 消息项组件：单条消息的展示（区分用户和AI）
- 加载指示器：显示AI正在输入状态

### 3.4 输入组件
- 文本输入框：支持多行和自适应高度
- 发送按钮：触发消息发送
- 快捷键支持：Enter发送，Shift+Enter换行

## 4. 样式设计

### 4.1 颜色方案
- 主色调：蓝色系（用于重要操作和链接）
- 背景色：浅灰色（主背景），白色（内容区域）
- 用户消息背景：浅蓝色
- AI消息背景：浅绿色
- 错误状态：红色系

### 4.2 响应式设计
- 桌面端：左右分栏布局
- 移动端：
  - 默认隐藏左侧面板
  - 点击菜单按钮展开左侧面板
  - 上下布局（顶部导航、中间聊天、底部输入）

### 4.3 动画效果
- 消息出现动画：淡入效果
- 页面切换动画：滑动过渡
- 加载状态动画：旋转指示器

## 5. 状态管理

### 5.1 用户状态
- 认证令牌：JWT
- 用户信息：用户名等基本信息

### 5.2 对话状态
- 对话列表：用户所有对话
- 当前对话：正在查看的对话ID
- 对话标题：每个对话的标识

### 5.3 消息状态
- 消息列表：当前对话的所有消息
- 加载状态：是否正在接收AI回复
- 错误状态：是否有错误发生

## 6. API接口对接

### 6.1 认证相关
- `POST /api/login`：用户登录获取JWT令牌
- `GET /api/user/me`：获取当前用户信息

### 6.2 对话相关
- `GET /api/conversations`：获取对话列表
- `POST /api/conversations/new`：创建新对话
- `DELETE /api/conversations/{conversation_id}`：删除对话

### 6.3 消息相关
- `GET /api/messages?conversation_id={id}`：获取对话消息
- `POST /api/chat/stream`：发送消息并接收流式响应
- `GET /api/chat/stream`：GET方式发送消息并接收流式响应

## 7. 错误处理

### 7.1 网络错误
- 显示网络连接失败提示
- 提供重试机制

### 7.2 认证错误
- JWT过期或无效时跳转回登录页
- 显示认证失败提示

### 7.3 业务错误
- 根据后端返回的错误信息显示给用户
- 提供相应处理建议

## 8. 性能优化

### 8.1 消息渲染优化
- 虚拟滚动：大量消息时只渲染可见区域
- 消息缓存：避免重复渲染已加载的消息

### 8.2 数据加载优化
- 分页加载：历史消息分页加载
- 预加载：提前加载可能需要的数据

### 8.3 内存管理
- 及时清理不需要的组件和数据
- 避免内存泄漏