# AI回复引用功能设计文档

## 1. 概述

本文档详细描述了AI聊天系统中引用内容显示功能的设计与实现方案。根据需求，系统需要实现以下功能：

1. AI回复需要返回引用的内容
2. 引用内容前端用markdown格式显示，可以折叠和展开，折叠时为一行
3. AI回复内容同样以markdown格式显示

## 2. 功能需求

### 2.1 核心功能
1. AI回复中包含引用内容
2. 引用内容以Markdown格式显示
3. 引用内容支持折叠/展开交互
4. 折叠状态下显示为单行
5. 展开状态下显示完整内容

### 2.2 显示要求
1. AI回复内容同样以Markdown格式显示
2. 引用内容与AI回复内容分离显示
3. 引用内容区域有明确的视觉标识

## 3. 设计方案

### 3.1 后端数据结构设计

#### 3.1.1 消息数据模型扩展

在现有的消息模型中添加引用内容字段：

```python
# models/message.py
class Message(Base):
    references = Column(JSON, nullable=True)  # 存储引用内容的JSON数组
```

#### 3.1.2 引用内容数据结构

引用内容将采用以下JSON格式：

```json
{
  "content": "根据参考资料，人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。",
  "references": [
    {
      "id": 1,
      "title": "人工智能简介",
      "content": "人工智能（Artificial Intelligence），英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。",
      "source": "https://example.com/ai-intro",
      "score": 0.95
    },
    {
      "id": 2,
      "title": "机器学习基础",
      "content": "机器学习是人工智能的一个子集，它使计算机能够从数据中学习并做出决策或预测。",
      "source": "https://example.com/ml-basics",
      "score": 0.87
    }
  ]
}
```

### 3.2 前端显示设计

#### 3.2.1 整体布局

```
+-------------------------------------------------------------+
| AI回复内容（Markdown格式）                                  |
|                                                             |
| 这里是AI的回复内容，支持Markdown格式显示...                 |
|                                                             |
+-------------------------------------------------------------+
| [参考资料] [▼]                                              |
+-------------------------------------------------------------+
| 引用内容1（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
| 引用内容2（折叠状态下显示单行）                             |
+-------------------------------------------------------------+
```

#### 3.2.2 折叠状态
- 默认状态下，引用内容区域处于折叠状态
- 每个引用内容仅显示标题和内容的前N个字符，以单行形式展示
- 右上角显示展开/折叠图标（▼/▲）

#### 3.2.3 展开状态
- 用户点击参考资料区域或展开按钮后，引用内容完全展开
- 显示引用内容的完整信息，包括标题和内容
- 支持Markdown格式渲染
- 右上角显示折叠按钮（▲）

### 3.3 交互设计

#### 3.3.1 展开/折叠操作
1. 点击"参考资料"标题区域可切换折叠/展开状态
2. 点击每个引用条目前的展开/折叠图标可单独控制该条目
3. 提供"展开全部"/"折叠全部"按钮

#### 3.3.2 引用内容操作
1. 点击引用标题可跳转到原始链接（如果有URL）
2. 支持复制引用内容
3. 提供引用内容的分享功能

## 5. 测试方案

### 5.1 功能测试
1. 验证AI回复内容正确显示
2. 验证引用内容正确显示
3. 验证折叠/展开功能正常
4. 验证Markdown渲染正确
5. 验证URL跳转功能正常

### 5.2 兼容性测试
1. 测试不同浏览器的兼容性
2. 测试移动端显示效果
3. 测试无引用内容时的显示
4. 测试大量引用内容时的性能

## 7. 注意事项

1. 引用内容可能较长，需要考虑性能优化
2. 需要处理Markdown渲染的安全性问题
3. 要确保在不同屏幕尺寸下的显示效果
4. 引用内容的URL需要进行安全检查
5. 需要考虑网络中断等异常情况下的处理