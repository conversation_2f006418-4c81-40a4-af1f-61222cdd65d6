## 需求

1. 设计一个登录页面，需要有账号密码，以及登录按钮即可

2. 设计一个聊天界面页面，左侧为历史消息列表，历史消息列表上有一个按钮为新建对话，新建对话上方为logo及name和一个折叠左侧页面的按钮，如下图：

   ![image-20250723101541363](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250723101541363.png)

3. 聊天界面右侧为主聊天区域，会显示历史会话信息，同时会在下方有输入框，右侧时发送信息按钮，点击按钮发送信息，调用后端接口，后端会以sse流式返回信息

## 设计要求：

1. 按照核心功能和需求分析中的描述，并详细分析项目结构和各模块功能，生成一份详细的前端页面设计方案
2. 注意要尽量详细，目标是可以根据生成的技术方案直接生成可用代码
3. 技术方案中不用生成具体代码
4. 使用技术选型中提供的技术栈以及ui图标库
5. 不要前后端分离

## 技术选型

- 前端可以采用vue开发
- TailwindCSS - Utility-first CSS framework
- shadcn-ui - UI component built using Radix UI and Tailwind CSS
- shadcn-chat - Chat components for NextJS/React 
- Framer Motion - Motion/animation library 
- Lucide Icons - Icon library

