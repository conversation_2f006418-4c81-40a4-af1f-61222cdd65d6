## 优化方案

### 针对conversation列表的优化
1. 优化获取对话列表接口，需要在每条数据中返回stickyFlag，当该字段为True时，则置顶本条数据。
2. stickyFlag需要存入数据库中的conversation表，新建会话时默认为False
3. 用户点击页面上的置顶按钮时，调用接口更新该条conversation记录，并将该条数据置顶
4. 每一个conversation都需要有create_at,update_at字段
5. 列表分组展示，分别为当天，最近一周，最近30天

### 针对message的优化
1. 每一个message都需要有create_at,update_at字段
2. 每一个message都需要有parentMsgId,parentMsgId为上一条消息的id，第一条消息的parentMsgId为0
3. 根据parentMsgId来展示message，parentMsgId为0的message为第一条消息，parentMsgId不为0的message为回复消息，根据不同role左右排列

### 需要做一个logout的功能
1. 创建一个logout接口，用户点击logout按钮时，调用该接口，去掉用户的登录状态，并返回登登陆页面

