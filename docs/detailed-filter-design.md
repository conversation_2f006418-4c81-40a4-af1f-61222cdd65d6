# 详细过滤条件设计文档

## 1. 需求概述

根据过滤条件设计文档的要求，用户在新建对话后，在聊天页面需要选择知识库(collection_name)以及对应的class_tag，不选择则不允许输入问题。这些过滤条件需要存储在消息中，其中collection_name为必填项，class_tag为可选项，并通过/chat/stream接口中的input参数传递。

## 2. 当前系统分析

### 2.1 现有功能分析

1. 当前系统已支持在聊天接口中传递[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L69)对象，包含[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)和[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L51-L55)字段
2. [Message](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L11-L26)模型中已包含[references](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L20-L20)字段用于存储引用内容，但缺少存储[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L35-L41)和[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)的字段
3. 前端聊天页面目前没有选择[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)和[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)的界面
4. 后端聊天接口已支持通过[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L51-L55)参数传递额外的过滤参数

### 2.2 系统架构图

```mermaid
graph TD
    A[前端聊天页面] --> B[聊天接口 /api/chat/stream]
    B --> C[消息服务 MessageService]
    C --> D[LLM服务 LLMService]
    D --> E[RAG检索器 RAGRetriever]
    E --> F[Milvus向量数据库]
    
    subgraph 后端服务
        B
        C
        D
        E
        F
    end
    
    G[对话管理接口] --> C
    H[消息管理接口] --> C
```

## 3. 数据模型设计

### 3.1 Message模型扩展

当前[Message](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L11-L26)模型需要添加两个新字段来存储过滤条件：

```python
class Message(Base):
    # ... 现有字段 ...
    
    # 新增字段
    collection_name = Column(String(100), nullable=True)  # 知识库名称
    input = Column(JSON, nullable=True)  # 存储额外的输入参数，包括class_tag等
```

### 3.2 ChatRequest Schema

当前[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L69)已经包含必要的字段，无需修改：

```python
class ChatRequest(BaseModel):
    collection_name: str = Field(
        ...,
        description="知识库集合名称",
        min_length=1,
        max_length=100,
        pattern="^[a-zA-Z0-9_-]+$"
    )
    # ... 其他字段 ...
    input: Dict[str, Any] = Field(
        default_factory=dict,
        description="额外输入参数"
    )
```

## 4. 后端实现方案

### 4.1 数据库模型更新

需要在[Message](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L11-L26)模型中添加[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L35-L41)和[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L51-L55)字段：

```python
# models/message.py
class Message(Base):
    # ... 现有字段 ...
    
    # 新增字段
    collection_name = Column(String(100), nullable=True)  # 知识库名称
    input = Column(JSON, nullable=True)  # 存储额外的输入参数，包括class_tag等
```

### 4.2 DAO层更新

更新[MessageDao.create](file:///D:/code/chatbot-ui/chatbot/backend/crud/message_dao.py#L83-L127)方法以支持新字段：

```python
# crud/message_dao.py
@staticmethod
def create(
    db: Session,
    conversation_id: int,
    role: str,
    content: str,
    parent_msg_id: int = 0,
    references: List[Dict] = None,
    collection_name: str = None,  # 新增参数
    input: Dict = None,  # 新增参数
    auto_commit: bool = True
) -> Message:
    # ... 现有代码 ...
    message = Message(
        # ... 现有参数 ...
        collection_name=collection_name,  # 新增字段
        input=input,  # 新增字段
    )
    # ... 其余代码 ...
```

### 4.3 Service层更新

更新[MessageService](file:///D:/code/chatbot-ui/chatbot/backend/services/message_service.py#L17-L137)中的方法以传递新字段：

```python
# services/message_service.py
def create_user_message(
    self, db: Session, conversation_id: int, content: str, username: str, 
    parent_msg_id: int = 0, collection_name: str = None, input: Dict = None  # 新增参数
) -> Message:
    # ... 现有代码 ...
    return MessageDao.create(
        db, conversation_id, "user", content, parent_msg_id, 
        collection_name=collection_name, input=input  # 传递新参数
    )

def create_assistant_message(
    self, db: Session, conversation_id: int, content: str, 
    parent_msg_id: int = 0, references: List[Dict] = None,
    collection_name: str = None, input: Dict = None  # 新增参数
) -> Message:
    return MessageDao.create(
        db, conversation_id, "assistant", content, parent_msg_id, references,
        collection_name=collection_name, input=input  # 传递新参数
    )
```

### 4.4 API层更新

更新聊天接口以确保[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)和[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L51-L55)正确传递到服务层：

```python
# api/chat.py
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    # ... 现有代码 ...
    user_message = message_service.create_user_message(
        db, chat_data.conversation_id, chat_data.message, current_username, 
        chat_data.parent_msg_id, chat_data.collection_name, chat_data.input  # 传递新参数
    )
    # ... 现有代码 ...
    message_service.create_assistant_message(
        db, chat_data.conversation_id, ai_response_content, 
        user_message.id, references, chat_data.collection_name, chat_data.input  # 传递新参数
    )
```

## 5. 前端实现方案

### 5.1 UI设计

在聊天界面中添加过滤条件选择区域：

1. 在新建对话时显示知识库选择下拉框
2. 根据选择的知识库动态加载对应的class_tag选项
3. 在发送消息前验证过滤条件是否已选择

### 5.2 功能实现

1. 添加知识库选择组件
2. 添加class_tag选择组件
3. 更新消息发送逻辑以包含过滤条件
4. 在对话创建时保存默认过滤条件

### 5.3 前端代码更新

需要在前端聊天页面添加以下功能：

```javascript
// static/js/chat.js
// 添加过滤条件相关状态
const collectionName = ref('');
const classTag = ref('');
const availableCollections = ref([]);
const availableClassTags = ref([]);

// 获取可用的知识库列表
const loadCollections = async () => {
    try {
        // TODO: 实现获取知识库列表的API调用
        // 这里应该调用后端API获取可用的知识库列表
        availableCollections.value = ['FinancialResearchOffice', 'TechnicalDocumentation', 'HRPolicy'];
    } catch (error) {
        console.error('Failed to load collections:', error);
    }
};

// 根据选择的知识库加载class_tag选项
const loadClassTags = async (selectedCollection) => {
    try {
        // TODO: 实现根据知识库获取class_tag的API调用
        // 这里应该调用后端API获取指定知识库的class_tag列表
        if (selectedCollection === 'FinancialResearchOffice') {
            availableClassTags.value = ['AnnualReport', 'FinancialStatement', 'InvestmentGuide'];
        } else {
            availableClassTags.value = [];
        }
        classTag.value = ''; // 重置class_tag选择
    } catch (error) {
        console.error('Failed to load class tags:', error);
    }
};

// 更新消息发送逻辑
const sendMessage = async () => {
    // ... 现有代码 ...
    
    // 构造包含过滤条件的input参数
    const inputParams = {};
    if (classTag.value) {
        inputParams.class_tag = classTag.value;
    }
    
    await apiService.sendStreamMessage(
        conversationId,
        message,
        {
            // 添加过滤条件到请求中
            collection_name: collectionName.value,
            input: inputParams,
            // ... 其他回调 ...
        }
    );
    
    // ... 现有代码 ...
};
```

## 6. 数据流设计

### 6.1 过滤条件选择流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant R as RAG检索器
    participant M as Milvus

    U->>F: 新建对话
    F->>B: 请求可用知识库列表
    B-->>F: 返回知识库列表
    F->>U: 显示知识库选择界面
    
    U->>F: 选择知识库
    F->>B: 请求该知识库的class_tag列表
    B-->>F: 返回class_tag列表
    F->>U: 显示class_tag选择界面
    
    U->>F: 输入问题并发送
    F->>B: 发送消息(包含collection_name和input)
    B->>R: 调用RAG检索器(传递过滤条件)
    R->>M: 在指定知识库中检索(应用class_tag过滤)
    M-->>R: 返回检索结果
    R->>B: 返回带引用的回复
    B-->>F: 流式返回回复
    F->>U: 显示回复
```

### 6.2 数据存储流程

1. 用户选择过滤条件（[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)和[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)）
2. 发送消息时将过滤条件包含在请求中
3. 后端创建用户消息时将过滤条件存储到数据库
4. 生成AI回复时使用过滤条件进行检索
5. 创建助手消息时将过滤条件和引用内容一同存储

## 7. API接口设计

### 7.1 现有接口更新

当前的`/api/chat/stream`接口已经支持所需参数，无需更改接口定义：

```
POST /api/chat/stream
Content-Type: application/json

{
  "conversation_id": 1,
  "collection_name": "FinancialResearchOffice",
  "message": "请解释什么是自然语言处理？",
  "input": {
    "class_tag": "TechnicalDocumentation"
  }
}
```

### 7.2 新增接口

需要添加获取知识库和class_tag列表的接口：

```
GET /api/collections
获取可用的知识库列表

Response:
{
  "code": 200,
  "message": "success",
  "data": [
    "FinancialResearchOffice",
    "TechnicalDocumentation",
    "HRPolicy"
  ]
}
```

```
GET /api/collections/{collection_name}/class-tags
获取指定知识库的class_tag列表

Response:
{
  "code": 200,
  "message": "success",
  "data": [
    "AnnualReport",
    "FinancialStatement",
    "InvestmentGuide"
  ]
}
```

## 8. 安全性和验证

### 8.1 输入验证

1. 验证[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)是否为系统支持的有效值
2. 验证[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)是否为指定[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)下的有效值
3. 对[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L51-L55)参数进行大小和内容限制

### 8.2 权限控制

1. 确保用户只能访问其有权限的知识库
2. 验证用户是否可以使用指定的[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)

## 9. 错误处理

### 9.1 常见错误场景

1. 未选择[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)就尝试发送消息
2. 选择的[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)或[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)无效
3. 知识库或[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)服务不可用

### 9.2 错误响应格式

```json
{
  "code": 1001,
  "message": "参数错误: 请选择有效的知识库",
  "data": null
}
```

## 10. 测试方案

### 10.1 单元测试

1. 测试[Message](file:///D:/code/chatbot-ui/chatbot/backend/models/message.py#L11-L26)模型的字段扩展
2. 测试DAO层对新字段的处理
3. 测试Service层传递过滤条件的正确性
4. 测试API层参数验证逻辑

### 10.2 集成测试

1. 测试完整的过滤条件选择和消息发送流程
2. 测试不同[collection_name](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L35-L41)和[class_tag](file:///code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L37-L37)组合的效果
3. 测试错误情况下的响应处理

### 10.3 端到端测试

1. 在UI上测试过滤条件选择功能
2. 验证过滤条件正确传递到后端
3. 验证检索结果符合过滤条件
4. 验证过滤条件被正确存储在消息中

## 11. 部署和维护

### 11.1 数据库迁移

需要创建数据库迁移脚本来添加新字段：

```sql
ALTER TABLE message ADD COLUMN collection_name VARCHAR(100);
ALTER TABLE message ADD COLUMN input JSON;
```

### 11.2 配置管理

1. 确保配置文件中包含可用的知识库列表
2. 为不同环境配置不同的知识库和class_tag映射关系

## 12. 未来扩展

### 12.1 功能扩展

1. 支持更复杂的过滤条件
2. 允许用户自定义过滤规则
3. 提供过滤条件的历史记录和推荐

### 12.2 性能优化

1. 缓存知识库和class_tag列表
2. 优化过滤条件的验证逻辑
3. 提供异步加载过滤条件选项的功能