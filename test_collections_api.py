#!/usr/bin/env python3
"""Test script for collections API"""

import requests
import json

# Test the collections config endpoint
def test_collections_config():
    """Test the collections config API endpoint"""
    
    # First, let's try to login to get a token
    login_url = "http://localhost:8001/api/login"
    login_data = {
        "username": "test_user",
        "password": "test_password"
    }
    
    try:
        # Lo<PERSON> to get token
        print("Testing login...")
        login_response = requests.post(login_url, json=login_data)
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response: {login_response.text}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("code") == 200:
                token = login_result["data"]["access_token"]
                print(f"Login successful, token: {token[:20]}...")
                
                # Test collections config endpoint
                print("\nTesting collections config endpoint...")
                config_url = "http://localhost:8001/api/collections/config"
                headers = {"Authorization": f"Bearer {token}"}
                
                config_response = requests.get(config_url, headers=headers)
                print(f"Collections config response status: {config_response.status_code}")
                print(f"Collections config response: {json.dumps(config_response.json(), indent=2, ensure_ascii=False)}")
                
                return config_response.json()
            else:
                print(f"Login failed: {login_result.get('message')}")
        else:
            print(f"Login request failed with status {login_response.status_code}")
            
    except Exception as e:
        print(f"Error testing collections API: {e}")
        return None

if __name__ == "__main__":
    print("Testing Collections API...")
    print("=" * 50)
    result = test_collections_config()
    
    if result and result.get("code") == 200:
        print("\n✅ Collections API test passed!")
        print(f"Found {len(result['data'])} collections:")
        for collection in result['data']:
            print(f"  - {collection['display_name']} ({collection['name']})")
            if collection['filter']:
                print(f"    Filters: {len(collection['filter'])}")
                for filter_item in collection['filter']:
                    print(f"      - {filter_item['display_name']}: {filter_item['options']}")
    else:
        print("\n❌ Collections API test failed!")
