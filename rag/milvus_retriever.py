from typing import List, Any, Dict
import logging

from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever

from rag.vectorstore_search import VectorStore_Search

# 配置日志
logger = logging.getLogger(__name__)


class MilvusRetriever(BaseRetriever):
    """Milvus检索器实现"""

    def __init__(self, milvus_service: VectorStore_Search):
        super().__init__()
        self.milvus_service = milvus_service

    def _get_relevant_documents(self, query: str) -> List[Document]:
        """获取相关文档"""
        try:
            # 使用search_hybrid_parent_child进行检索
            results = self.milvus_service.search_hybrid_parent_child(
                query=query,
                k=5,
                semantic_weight=0.7,
                min_score=0.1,
                sort_by="max_score",
            )
            # 转换为LangChain Document格式
            documents = []
            for i, result in enumerate(results):
                content = self._combine_parent_child_content(result)
                metadata = self._extract_metadata(result, i)
                documents.append(Document(page_content=content, metadata=metadata))

            return documents

        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []

    def _combine_parent_child_content(self, result: Dict) -> str:
        """组合父子文档内容"""
        combined = ""
        ref_i = 1

        # 获取父文档信息
        parent_doc = result.get("parent_document", "")
        parent_metadata = result.get("parent_metadata", {})
        ref_doc = parent_metadata.get("filename", "未知文档")
        ref_version = parent_metadata.get("version", "未知版本")

        # 添加父文档内容
        temp_out = f"# 参考资料{ref_i}\n\n- 来源：{ref_doc}\n- 版本：{ref_version}\n\n```markdown\n{parent_doc}\n```\n\n"

        if len(temp_out) <= 15000:
            combined = temp_out

        return combined

    def _extract_metadata(self, result: Dict, rank: int) -> Dict:
        """提取元数据"""
        parent_metadata = result.get("parent_metadata", {})
        search_stats = result.get("search_stats", {})

        return {
            "source_id": parent_metadata.get("id", ""),
            "score": search_stats.get("max_score", 0.0),
            "rank": rank,
            "title": parent_metadata.get("title", ""),
            "source": parent_metadata.get("source", ""),
            "page": parent_metadata.get("page", 0),
            "filename": parent_metadata.get("filename", ""),
            "version": parent_metadata.get("version", ""),
            "search_type": result.get("search_type", "hybrid_parent_child"),
            "hit_count": search_stats.get("hit_count", 0),
            "avg_score": search_stats.get("avg_score", 0.0),
            "total_score": search_stats.get("total_score", 0.0),
        }
