"""Collection schemas for request/response validation."""

from typing import List, Optional
from pydantic import BaseModel, Field


class FilterOption(BaseModel):
    """过滤条件选项schema"""
    
    name: str = Field(..., description="过滤条件名称")
    display_name: str = Field(..., description="过滤条件显示名称")
    description: str = Field(..., description="过滤条件描述")
    options: List[str] = Field(..., description="可选项列表")


class CollectionConfig(BaseModel):
    """知识库配置schema"""
    
    name: str = Field(..., description="知识库名称")
    display_name: str = Field(..., description="知识库显示名称")
    description: str = Field(..., description="知识库描述")
    filter: List[FilterOption] = Field(default=[], description="过滤条件列表")


class CollectionConfigListResponse(BaseModel):
    """知识库配置列表响应schema"""
    
    code: int = Field(default=200, description="响应状态码")
    message: str = Field(default="success", description="响应消息")
    data: List[CollectionConfig] = Field(..., description="知识库配置列表")
