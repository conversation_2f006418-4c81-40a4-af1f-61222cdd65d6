"""Standard response schemas for API consistency."""

from typing import TypeVar, Generic, Optional, Any, List
from pydantic import BaseModel

DataType = TypeVar('DataType')


class StandardResponse(BaseModel, Generic[DataType]):
    """Standard response schema for single object data."""
    code: int
    message: str
    data: Optional[DataType]


class StandardListResponse(BaseModel, Generic[DataType]):
    """Standard response schema for list data."""
    code: int
    message: str
    data: List[DataType]


class StandardErrorResponse(BaseModel):
    """Standard error response schema."""
    code: int
    message: str
    data: Optional[Any] = None
    details: Optional[Any] = None