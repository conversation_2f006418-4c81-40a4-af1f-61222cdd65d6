"""Message schemas for request/response validation."""

from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, validator, ConfigDict


class ReferenceItem(BaseModel):
    """引用内容项schema"""

    id: int = Field(..., description="引用ID")
    title: str = Field(..., description="引用标题")
    content: str = Field(..., description="引用内容")
    source: Optional[str] = Field(None, description="引用来源URL")
    score: float = Field(..., description="相关度得分")


class MessageResponse(BaseModel):
    """Message response schema."""

    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="消息ID")
    role: str = Field(..., description="消息角色", pattern="^(user|assistant)$")
    content: str = Field(..., description="消息内容", min_length=1)
    references: Optional[List[ReferenceItem]] = Field(None, description="引用内容列表")
    parent_msg_id: int = Field(default=0, description="父消息ID")
    timestamp: datetime = Field(..., description="消息时间戳")  # 保持兼容性
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ChatRequest(BaseModel):
    """Chat request schema for sending messages."""

    collection_name: str = Field(
        ...,
        description="知识库集合名称",
        min_length=1,
        max_length=100,
        pattern="^[a-zA-Z0-9_-]+$"
    )
    conversation_id: int = Field(
        ...,
        description="会话ID",
        gt=0
    )
    message: str = Field(
        ...,
        description="用户消息内容",
        min_length=1,
        max_length=4000
    )
    input: Dict[str, Any] = Field(
        default_factory=dict,
        description="额外输入参数"
    )
    parent_msg_id: int = Field(default=0, description="父消息ID")

    @validator('message')
    def validate_message_content(cls, v):
        """验证消息内容"""
        if not v.strip():
            raise ValueError('消息内容不能为空或只包含空白字符')

        # 检查是否包含恶意内容（简单示例）
        forbidden_patterns = ['<script', 'javascript:', 'data:']
        v_lower = v.lower()
        for pattern in forbidden_patterns:
            if pattern in v_lower:
                raise ValueError('消息内容包含不允许的内容')

        return v.strip()

    @validator('input')
    def validate_input_params(cls, v):
        """验证输入参数"""
        if not isinstance(v, dict):
            raise ValueError('input参数必须是字典类型')

        # 限制输入参数的大小
        if len(str(v)) > 1000:
            raise ValueError('input参数过大')

        return v


class MessagesResponse(BaseModel):
    """Messages response schema with conversation ID as key."""

    messages: Dict[int, List[MessageResponse]] = Field(
        ...,
        description="以会话ID为键的消息字典"
    )


class MessageCreateRequest(BaseModel):
    """创建消息请求schema"""

    conversation_id: int = Field(..., description="会话ID", gt=0)
    content: str = Field(
        ...,
        description="消息内容",
        min_length=1,
        max_length=4000
    )
    role: str = Field(
        default="user",
        description="消息角色",
        pattern="^(user|assistant)$"
    )
    parent_msg_id: int = Field(default=0, description="父消息ID")

    @validator('content')
    def validate_content(cls, v):
        """验证消息内容"""
        if not v.strip():
            raise ValueError('消息内容不能为空')
        return v.strip()


class MessageUpdateRequest(BaseModel):
    """更新消息请求schema"""

    content: Optional[str] = Field(
        None,
        description="消息内容",
        min_length=1,
        max_length=4000
    )

    @validator('content')
    def validate_content(cls, v):
        """验证消息内容"""
        if v is not None and not v.strip():
            raise ValueError('消息内容不能为空')
        return v.strip() if v else v


class MessageListResponse(BaseModel):
    """消息列表响应schema"""

    messages: List[MessageResponse] = Field(..., description="消息列表")
    total: int = Field(..., description="消息总数", ge=0)
    page: int = Field(..., description="当前页码", ge=1)
    size: int = Field(..., description="每页大小", ge=1, le=100)
    has_more: bool = Field(..., description="是否有更多消息")
